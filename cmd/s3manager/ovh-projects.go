package s3manager

import (
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.mtk.zone/mt-public/s3manager/internal/providers/ovh"
)

// ovhProjectsCmd represents the ovh-projects command
var ovhProjectsCmd = &cobra.Command{
	Use:   "ovh-projects",
	Short: "List OVH projects",
	Long:  "List OVH projects using the configured OVH API credentials.",
	RunE:  runOvhProjects,
}

func init() {
	rootCmd.AddCommand(ovhProjectsCmd)
}

func runOvhProjects(cmd *cobra.Command, args []string) error {
	// Access the global configuration
	config := GetGlobalConfig()

	provider, err := ovh.NewProvider(config.OvhEndpoint, config.OvhApplicationKey, config.OvhApplicationSecret, config.OvhConsumerKey, config.OvhProjectId)
	if err != nil {
		return fmt.Errorf("failed to create OVH provider: %w", err)
	}

	result, err := provider.ExecApi("get", "/cloud/project", "")
	if err != nil {
		fmt.Printf("API call failed: %v", err)
		return err
	}

	// result = []any{
	// 	"valid-project-1",
	// 	123456, // This will cause type assertion error
	// 	"valid-project-2",
	// 	map[string]string{"invalid": "object"},
	// }

	// Type assert to []interface{} and then convert each element to string
	if projects, ok := result.([]any); ok {
		for i, project := range projects {
			if projectID, ok := project.(string); ok {
				fmt.Printf("%d: %s\n", i, projectID)
			} else {
				return fmt.Errorf("%d: %v (unexpected type: %T)", i, project, project)
			}
		}
	} else {
		return fmt.Errorf("unexpected result format, got type: %T", result)
	}

	return nil
}
